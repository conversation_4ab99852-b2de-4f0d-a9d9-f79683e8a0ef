import {
  Controller,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Logger,
  BadRequestException,
  InternalServerErrorException,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { CrawlerService } from './crawler.service';
import { CrawlRequestDto, CrawlResponseDto } from './dto';

@Controller('crawl')
export class CrawlerController {
  private readonly logger = new Logger(CrawlerController.name);

  constructor(private readonly crawlerService: CrawlerService) {}

  @Post()
  @HttpCode(HttpStatus.OK)
  @UsePipes(new ValidationPipe({ transform: true, whitelist: true }))
  async crawlUrl(
    @Body() crawlRequest: CrawlRequestDto,
  ): Promise<CrawlResponseDto> {
    this.logger.log(`Received crawl request for URL: ${crawlRequest.url}`);

    try {
      // Validate URL format
      if (!this.isValidUrl(crawlRequest.url)) {
        throw new BadRequestException('Invalid URL format provided');
      }

      // Start crawling
      const result = await this.crawlerService.crawlSingleUrl(
        crawlRequest.url,
        crawlRequest.maxConcurrency,
        crawlRequest.maxPages,
      );

      this.logger.log(
        `Crawl completed successfully for URL: ${crawlRequest.url}`,
      );
      return CrawlResponseDto.success(
        'Crawling completed successfully',
        result.data,
        result.crawlId,
      );
    } catch (error) {
      const errorMsg =
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred during crawling';
      const errorStack = error instanceof Error ? error.stack : undefined;
      this.logger.error(
        `Crawl failed for URL: ${crawlRequest.url}`,
        errorStack,
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      // Handle network errors, timeout errors, etc.
      throw new InternalServerErrorException(
        CrawlResponseDto.error(
          'Crawling failed',
          errorMsg ?? 'An unexpected error occurred during crawling',
        ),
      );
    }
  }

  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
    } catch {
      return false;
    }
  }
}
