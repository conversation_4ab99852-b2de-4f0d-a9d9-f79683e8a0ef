export interface CrawledData {
  url: string;
  title: string;
  crawledAt: Date;
}

export class CrawlResponseDto {
  success: boolean;
  message: string;
  data?: CrawledData[];
  error?: string;
  crawlId?: string;
  totalPages?: number;
  startedAt: Date;
  completedAt?: Date;

  constructor(
    success: boolean,
    message: string,
    data?: CrawledData[],
    error?: string,
    crawlId?: string,
  ) {
    this.success = success;
    this.message = message;
    this.data = data;
    this.error = error;
    this.crawlId = crawlId;
    this.startedAt = new Date();
    this.totalPages = data?.length || 0;
  }

  static success(message: string, data: CrawledData[], crawlId?: string): CrawlResponseDto {
    const response = new CrawlResponseDto(true, message, data, undefined, crawlId);
    response.completedAt = new Date();
    return response;
  }

  static error(message: string, error: string): CrawlResponseDto {
    return new CrawlResponseDto(false, message, undefined, error);
  }
}
