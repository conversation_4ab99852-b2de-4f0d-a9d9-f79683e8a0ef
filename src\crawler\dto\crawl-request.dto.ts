import {
  IsUrl,
  <PERSON><PERSON>otEmpt<PERSON>,
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';

export class CrawlRequestDto {
  @IsNotEmpty()
  @IsUrl({}, { message: 'Please provide a valid URL' })
  url: string;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(10)
  maxConcurrency?: number = 5;

  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  maxPages?: number = 10;
}
