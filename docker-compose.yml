version: '3.8'

services:
  # Main NestJS Application
  nest-app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: nest-crawler-app
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: production
      PORT: 3000
    volumes:
      # Persist crawled data
      - crawler_storage:/app/storage
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis for caching and session storage (optional but recommended for production)
  redis:
    image: redis:7-alpine
    container_name: nest-crawler-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  crawler_storage:
    driver: local
  redis_data:
    driver: local

networks:
  default:
    name: nest-crawler-network
