# Environment Configuration Template
# Copy this file to .env and configure the values for your environment

# Application Configuration
NODE_ENV=development
PORT=3000

# Logging Configuration
LOG_LEVEL=info

# Crawler Configuration
CRAWL_START_URLS=[https://example.com]
CRAWL_CONCURRENCY=5
CRAWL_MAX_PAGES=10
CRAWL_TIMEOUT=30000

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# Database Configuration (optional - uncomment if using a database)
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=nest_crawler
# DATABASE_USER=postgres
# DATABASE_PASSWORD=postgres
# DATABASE_SSL=false

# MongoDB Configuration (optional - uncomment if using MongoDB)
# MONGODB_URI=mongodb://localhost:27017/nest_crawler

# External API Configuration
# EXTERNAL_API_KEY=your_api_key_here
# EXTERNAL_API_URL=https://api.example.com

# Security Configuration
# JWT_SECRET=your_jwt_secret_here
# CORS_ORIGIN=http://localhost:3000

# File Storage Configuration
STORAGE_PATH=./storage
STORAGE_MAX_SIZE=**********  # 1GB in bytes

# Development Tools (set to false in production)
ENABLE_SWAGGER=false
ENABLE_DEBUG_ROUTES=false

# Performance Configuration
REQUEST_TIMEOUT=30000
MAX_CONCURRENT_REQUESTS=10

# Monitoring and Health Checks
HEALTH_CHECK_ENABLED=true
METRICS_ENABLED=false
