import {
  Injectable,
  OnModuleInit,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { CheerioCrawler, Dataset } from 'crawlee';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { CrawledData } from './dto';

export interface CrawlResult {
  data: CrawledData[];
  crawlId: string;
  totalPages: number;
}

@Injectable()
export class CrawlerService implements OnModuleInit {
  private readonly logger = new Logger(CrawlerService.name);

  constructor(private readonly config: ConfigService) {}

  async onModuleInit() {
    const startUrlsString = this.config.get<string>('CRAWL_START_URLS');
    if (startUrlsString) {
      const cleanedUrlsString = startUrlsString.replace(/^\[|\]$/g, ''); // Remove leading/trailing brackets
      const startUrls = cleanedUrlsString
        .split(',')
        .map((url) => url.trim())
        .filter((url) => url.length > 0);

      this.logger.log(`Parsed CRAWL_START_URLS: ${JSON.stringify(startUrls)}`);

      if (startUrls.length > 0) {
        this.logger.log('Starting automatic crawl on module initialization');
        await this.crawlUrls(startUrls);
      }
    }
  }

  /**
   * Crawl a single URL on-demand
   */
  async crawlSingleUrl(
    url: string,
    maxConcurrency: number = 5,
    maxPages: number = 10,
  ): Promise<CrawlResult> {
    this.validateUrl(url);

    const crawlId = this.generateCrawlId();
    this.logger.log(`Starting crawl for URL: ${url} (ID: ${crawlId})`);

    const crawledData: CrawledData[] = [];
    let pageCount = 0;

    const crawler = new CheerioCrawler({
      requestHandler: async ({ $, request, enqueueLinks }) => {
        if (pageCount >= maxPages) {
          this.logger.log(
            `Reached maximum page limit (${maxPages}) for crawl ${crawlId}`,
          );
          return;
        }

        this.logger.log(
          `Crawling ${request.url} (${pageCount + 1}/${maxPages})`,
        );

        try {
          // Extract data
          const title = $('title').text().trim() || 'No title found';
          const crawledItem: CrawledData = {
            url: request.url,
            title,
            crawledAt: new Date(),
          };

          crawledData.push(crawledItem);
          pageCount++;

          // Save to dataset for persistence
          await Dataset.pushData(crawledItem);

          // Enqueue further links only if we haven't reached the limit
          if (pageCount < maxPages) {
            await enqueueLinks({
              selector: 'a[href]',
              limit: Math.min(5, maxPages - pageCount), // Limit additional links
            });
          }
        } catch (error) {
          this.logger.error(
            `Error processing ${request.url}:`,
            error instanceof Error ? error.message : String(error),
          );
        }
      },
      maxConcurrency,
      maxRequestsPerCrawl: maxPages,
      requestHandlerTimeoutSecs: 30,
    });

    try {
      await crawler.run([url]);
      this.logger.log(
        `Crawl completed for ${url} (ID: ${crawlId}). Pages crawled: ${crawledData.length}`,
      );

      return {
        data: crawledData,
        crawlId,
        totalPages: crawledData.length,
      };
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      this.logger.error(`Crawl failed for ${url} (ID: ${crawlId}):`, errorMsg);
      throw new Error(`Crawling failed: ${errorMsg}`);
    }
  }

  /**
   * Legacy method for crawling multiple URLs (used by cron job and module init)
   */
  private async crawlUrls(startUrls: string[]): Promise<void> {
    const concurrent = this.config.get<number>('CRAWL_CONCURRENCY', 5);

    const crawler = new CheerioCrawler({
      requestHandler: async ({ $, request, enqueueLinks }) => {
        this.logger.log(`Crawling ${request.url}`);
        try {
          // Extract data
          const title = $('title').text().trim() || 'No title found';
          const crawledItem: CrawledData = {
            url: request.url,
            title,
            crawledAt: new Date(),
          };

          // Save to dataset
          await Dataset.pushData(crawledItem);
          // Enqueue further links
          await enqueueLinks({ selector: 'a.next' });
        } catch (error) {
          this.logger.error(
            `Error processing ${request.url}:`,
            error instanceof Error ? error.message : String(error),
          );
        }
      },
      maxConcurrency: concurrent,
      requestHandlerTimeoutSecs: 30,
    });

    try {
      await crawler.run(startUrls);
      this.logger.log('Crawl finished');
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      this.logger.error('Crawl failed:', errorMsg);
      throw error;
    }
  }

  /**
   * Validate URL format and accessibility
   */
  private validateUrl(url: string): void {
    if (!url || typeof url !== 'string') {
      throw new BadRequestException('URL is required and must be a string');
    }

    try {
      const urlObj = new URL(url);
      if (urlObj.protocol !== 'http:' && urlObj.protocol !== 'https:') {
        throw new BadRequestException('URL must use HTTP or HTTPS protocol');
      }
    } catch {
      throw new BadRequestException('Invalid URL format provided');
    }
  }

  /**
   * Generate a unique crawl ID for tracking
   */
  private generateCrawlId(): string {
    return `crawl_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  async handleDailyCrawl() {
    this.logger.log('Daily crawl triggered by cron job');
    const startUrlsString = this.config.get<string>('CRAWL_START_URLS');
    if (startUrlsString) {
      const cleanedUrlsString = startUrlsString.replace(/^\[|\]$/g, ''); // Remove leading/trailing brackets
      const startUrls = cleanedUrlsString
        .split(',')
        .map((url) => url.trim())
        .filter((url) => url.length > 0);

      this.logger.log(
        `Parsed CRAWL_START_URLS for daily crawl: ${JSON.stringify(startUrls)}`,
      );

      if (startUrls.length > 0) {
        await this.crawlUrls(startUrls);
      } else {
        this.logger.warn('No start URLs configured for daily crawl');
      }
    } else {
      this.logger.warn('No start URLs configured for daily crawl');
    }
  }
}
