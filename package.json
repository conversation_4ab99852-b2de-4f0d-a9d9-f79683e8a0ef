{"name": "nest-crawler", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "docker:dev": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build", "docker:dev:detached": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build -d", "docker:prod": "docker-compose up --build", "docker:prod:detached": "docker-compose up --build -d", "docker:stop": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml down", "docker:stop:prod": "docker-compose down", "docker:logs": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f", "docker:logs:app": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml logs -f nest-app", "docker:shell": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec nest-app sh", "docker:clean": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml down -v --remove-orphans", "docker:clean:all": "docker system prune -a --volumes", "docker:rebuild": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml down && docker-compose -f docker-compose.yml -f docker-compose.dev.yml up --build", "docker:test": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec nest-app npm test", "docker:test:e2e": "docker-compose -f docker-compose.yml -f docker-compose.dev.yml exec nest-app npm run test:e2e"}, "dependencies": {"@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/platform-express": "^11.0.1", "@nestjs/schedule": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "crawlee": "^3.13.8", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/jest": "^29.5.14", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}