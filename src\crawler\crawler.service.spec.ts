import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { BadRequestException } from '@nestjs/common';
import { CrawlerService } from './crawler.service';

// Mock Crawlee
jest.mock('crawlee', () => ({
  CheerioCrawler: jest.fn().mockImplementation(() => ({
    run: jest.fn().mockResolvedValue(undefined),
  })),
  Dataset: {
    pushData: jest.fn().mockResolvedValue(undefined),
  },
}));

describe('CrawlerService', () => {
  let service: CrawlerService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CrawlerService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
      ],
    }).compile();

    service = module.get<CrawlerService>(CrawlerService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('crawlSingleUrl', () => {
    it('should successfully crawl a valid URL', async () => {
      const testUrl = 'https://example.com';
      const result = await service.crawlSingleUrl(testUrl);

      expect(result).toBeDefined();
      expect(result.data).toEqual([]);
      expect(result.crawlId).toMatch(/^crawl_\d+_[a-z0-9]+$/);
      expect(result.totalPages).toBe(0);
    });

    it('should throw BadRequestException for invalid URL', async () => {
      const invalidUrl = 'not-a-url';

      await expect(service.crawlSingleUrl(invalidUrl)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw BadRequestException for non-HTTP(S) URL', async () => {
      const ftpUrl = 'ftp://example.com';

      await expect(service.crawlSingleUrl(ftpUrl)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw BadRequestException for empty URL', async () => {
      await expect(service.crawlSingleUrl('')).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should use default parameters when not provided', async () => {
      const testUrl = 'https://example.com';
      const result = await service.crawlSingleUrl(testUrl);

      expect(result).toBeDefined();
      // Default maxConcurrency is 5, maxPages is 10
    });

    it('should respect custom maxConcurrency and maxPages', async () => {
      const testUrl = 'https://example.com';
      const result = await service.crawlSingleUrl(testUrl, 3, 5);

      expect(result).toBeDefined();
    });
  });

  describe('onModuleInit', () => {
    it('should not crawl when no start URLs are configured', async () => {
      mockConfigService.get.mockReturnValue(undefined);

      await service.onModuleInit();

      expect(configService.get).toHaveBeenCalledWith('CRAWL_START_URLS');
    });

    it('should not crawl when start URLs array is empty', async () => {
      mockConfigService.get.mockReturnValue([]);

      await service.onModuleInit();

      expect(configService.get).toHaveBeenCalledWith('CRAWL_START_URLS');
    });
  });

  describe('handleDailyCrawl', () => {
    it('should log warning when no start URLs are configured', async () => {
      mockConfigService.get.mockReturnValue(undefined);
      const loggerSpy = jest.spyOn(service['logger'], 'warn');

      await service.handleDailyCrawl();

      expect(loggerSpy).toHaveBeenCalledWith('No start URLs configured for daily crawl');
    });
  });
});
