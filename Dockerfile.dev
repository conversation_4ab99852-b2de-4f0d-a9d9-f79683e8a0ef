# Multi-stage Dockerfile for development and production

# Base stage with common dependencies
FROM node:20-alpine AS base

# Install system dependencies
RUN apk add --no-cache \
    wget \
    curl \
    git \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./

# Development stage
FROM base AS development

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY . .

# Create storage directory with proper permissions
RUN mkdir -p storage && chown -R node:node storage

# Switch to non-root user
USER node

# Expose application port and debug port
EXPOSE 3000 9229

# Default command for development (can be overridden)
CMD ["npm", "run", "start:dev"]

# Production build stage
FROM base AS build

# Install all dependencies for building
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Remove dev dependencies
RUN npm prune --omit=dev

# Production stage
FROM node:20-alpine AS production

# Install system dependencies for production
RUN apk add --no-cache wget && rm -rf /var/cache/apk/*

WORKDIR /app

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nestjs -u 1001

# Copy built application and dependencies from build stage
COPY --from=build --chown=nestjs:nodejs /app/dist ./dist
COPY --from=build --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=build --chown=nestjs:nodejs /app/package*.json ./

# Create storage directory with proper permissions
RUN mkdir -p storage && chown -R nestjs:nodejs storage

# Switch to non-root user
USER nestjs

# Expose application port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/ || exit 1

# Start the application
CMD ["node", "dist/main"]
